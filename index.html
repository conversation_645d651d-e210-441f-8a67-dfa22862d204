<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR文字识别</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div id="app">
        <!-- 主界面 -->
        <div id="main-view" class="view">
            <div class="main-container">
                <!-- 左侧操作面板 -->
                <div class="left-panel">
                    <div class="panel-header">
                        <h1 class="app-title">OCR识别</h1>
                        <div class="header-controls">
                            <button id="theme-toggle-main" class="header-btn" title="切换主题">🌓</button>
                            <button id="config-btn" class="config-btn">⚙️</button>
                        </div>
                    </div>

                    <!-- 图片预览区域 -->
                    <div class="image-preview-area">
                        <div id="image-preview" class="image-preview" style="display: none;">
                            <img id="preview-img" class="preview-img" alt="预览图片">
                        </div>
                        <div id="preview-placeholder" class="preview-placeholder">
                            <div class="placeholder-icon">🖼️</div>
                            <div class="placeholder-text">图片预览区域</div>
                            <div class="placeholder-hint">选择图片或截图后将在此显示</div>
                        </div>
                    </div>

                    <!-- 底部控制区域 -->
                    <div class="bottom-controls">
                        <div class="action-buttons">
                            <button id="screenshot-btn" class="action-btn primary">
                                <span class="btn-icon">📷</span>
                                <span class="btn-text">截图</span>
                            </button>
                            <button id="upload-btn" class="action-btn">
                                <span class="btn-icon">📁</span>
                                <span class="btn-text">选择</span>
                            </button>
                        </div>

                        <div class="status-info">
                            <div class="status-item">
                                <span class="status-label">服务:</span>
                                <span id="current-service" class="status-value">未配置</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">状态:</span>
                                <span id="recognition-status" class="status-value ready">就绪</span>
                            </div>
                        </div>
                    </div>

                    <div id="loading" class="loading-panel" style="display: none;">
                        <div class="loading-content">
                            <div class="spinner"></div>
                            <div class="loading-text">
                                <div class="loading-title">正在识别中...</div>
                                <div class="loading-desc">请稍候，正在处理您的图片</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧结果面板 -->
                <div class="right-panel">
                    <div class="result-header">
                        <h3 class="result-title">识别结果</h3>
                    </div>

                    <div class="result-content">
                        <textarea id="result-text" class="result-text" placeholder="识别结果将显示在这里，您可以直接编辑...&#10;&#10;💡 提示：&#10;• 点击左侧按钮开始识别&#10;• 支持截图和本地图片识别&#10;• 识别结果可直接编辑"></textarea>
                    </div>

                    <div class="result-controls">
                        <label class="checkbox-label">
                            <input type="checkbox" id="remove-linebreaks">
                            <span class="checkbox-text">去除换行符</span>
                        </label>
                        <div class="control-buttons">
                            <button id="copy-btn" class="control-btn primary">
                                <span class="btn-icon">📋</span>
                                <span class="btn-text">复制</span>
                            </button>
                            <button id="clear-btn" class="control-btn">
                                <span class="btn-icon">🗑️</span>
                                <span class="btn-text">清空</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 配置界面 -->
        <div id="config-view" class="view" style="display: none;">
            <div class="config-header">
                <button id="back-btn" class="back-btn">←</button>
                <h1 class="config-title">OCR配置</h1>
                <div class="theme-toggle">
                    <button id="theme-toggle-btn" class="theme-btn" title="切换主题">🌓</button>
                </div>
            </div>
            
            <div class="config-content">
                <div class="config-section">
                    <h3>OCR服务选择</h3>
                    <select id="ocr-service" class="select-input">
                        <option value="baidu">百度OCR</option>
                        <option value="tencent">腾讯云OCR</option>
                        <option value="aliyun">阿里云OCR</option>
                        <option value="llm">AI大模型</option>
                    </select>
                </div>
                
                <!-- 百度OCR配置 -->
                <div id="baidu-config" class="config-section">
                    <h4>百度OCR配置</h4>
                    <div class="form-group">
                        <label>API Key:</label>
                        <div class="input-with-toggle">
                            <input type="password" id="baidu-api-key" placeholder="请输入百度OCR API Key">
                            <button type="button" class="toggle-password" id="toggle-baidu-api-key" title="显示/隐藏API Key">
                                <span class="eye-icon">👁️</span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Secret Key:</label>
                        <div class="input-with-toggle">
                            <input type="password" id="baidu-secret-key" placeholder="请输入百度OCR Secret Key">
                            <button type="button" class="toggle-password" id="toggle-baidu-secret-key" title="显示/隐藏Secret Key">
                                <span class="eye-icon">👁️</span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>识别类型:</label>
                        <select id="baidu-type">
                            <option value="general_basic">通用文字识别</option>
                            <option value="accurate_basic">高精度文字识别</option>
                        </select>
                    </div>
                </div>
                
                <!-- 腾讯云OCR配置 -->
                <div id="tencent-config" class="config-section" style="display: none;">
                    <h4>腾讯云OCR配置</h4>
                    <div class="form-group">
                        <label>Secret ID:</label>
                        <div class="input-with-toggle">
                            <input type="password" id="tencent-secret-id" placeholder="请输入腾讯云Secret ID">
                            <button type="button" class="toggle-password" id="toggle-tencent-secret-id" title="显示/隐藏Secret ID">
                                <span class="eye-icon">👁️</span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Secret Key:</label>
                        <div class="input-with-toggle">
                            <input type="password" id="tencent-secret-key" placeholder="请输入腾讯云Secret Key">
                            <button type="button" class="toggle-password" id="toggle-tencent-secret-key" title="显示/隐藏Secret Key">
                                <span class="eye-icon">👁️</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 阿里云OCR配置 -->
                <div id="aliyun-config" class="config-section" style="display: none;">
                    <h4>阿里云OCR配置</h4>
                    <div class="form-group">
                        <label>Access Key ID:</label>
                        <div class="input-with-toggle">
                            <input type="password" id="aliyun-access-key" placeholder="请输入阿里云Access Key ID">
                            <button type="button" class="toggle-password" id="toggle-aliyun-access-key" title="显示/隐藏Access Key ID">
                                <span class="eye-icon">👁️</span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Access Key Secret:</label>
                        <div class="input-with-toggle">
                            <input type="password" id="aliyun-access-secret" placeholder="请输入阿里云Access Key Secret">
                            <button type="button" class="toggle-password" id="toggle-aliyun-access-secret" title="显示/隐藏Access Key Secret">
                                <span class="eye-icon">👁️</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- LLM配置 -->
                <div id="llm-config" class="config-section" style="display: none;">
                    <h4>LLM视觉模型配置</h4>
                    <div class="form-group">
                        <label>平台选择:</label>
                        <select id="llm-platform">
                            <option value="openai">OpenAI</option>
                            <option value="anthropic">Anthropic (Claude)</option>
                            <option value="google">Google (Gemini)</option>
                            <option value="custom">自定义平台</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>API Key:</label>
                        <div class="input-with-toggle">
                            <input type="password" id="llm-api-key" placeholder="请输入LLM API Key">
                            <button type="button" class="toggle-password" id="toggle-api-key" title="显示/隐藏API Key">
                                <span class="eye-icon">👁️</span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>API Base URL (可选):</label>
                        <input type="text" id="llm-base-url" placeholder="自定义API地址，留空使用默认">
                    </div>

                    <!-- 模型版本选择 -->
                    <div class="form-group">
                        <label>模型版本:</label>
                        <div class="model-selection">
                            <select id="llm-model-select" style="flex: 1;">
                                <option value="">请先选择平台</option>
                            </select>
                            <button type="button" id="refresh-models-btn" class="btn-small" style="margin-left: 10px;">刷新</button>
                        </div>
                    </div>

                    <!-- 自定义模型输入 -->
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="use-custom-model"> 使用自定义模型版本
                        </label>
                    </div>
                    <div class="form-group" id="custom-model-group" style="display: none;">
                        <label>自定义模型名称:</label>
                        <input type="text" id="custom-model-name" placeholder="例如: gemini-2.0-flash-exp, gpt-4-vision-preview, claude-3-5-sonnet-20241022">
                        <small class="form-hint">输入完整的模型名称，如 gemini-2.0-flash-exp 或 gpt-4-vision-preview</small>
                    </div>

                    <!-- 模型信息显示 -->
                    <div id="model-info" class="model-info" style="display: none;">
                        <h5>模型信息</h5>
                        <div id="model-details"></div>
                    </div>

                    <div class="form-group">
                        <label>最大Token数:</label>
                        <input type="number" id="llm-max-tokens" value="1000" min="100" max="8000" placeholder="1000">
                    </div>
                    <div class="form-group">
                        <label>识别提示词:</label>
                        <textarea id="llm-prompt" rows="3" placeholder="请识别图片中的所有文字内容，直接输出文字，不要添加任何解释。">请识别图片中的所有文字内容，直接输出文字，不要添加任何解释。</textarea>
                    </div>
                </div>
                
                <div class="config-actions">
                    <button id="save-config-btn" class="btn primary">保存配置</button>
                    <button id="test-config-btn" class="btn">测试连接</button>
                </div>
            </div>
        </div>
    </div>
    
    <input type="file" id="file-input" accept="image/*" style="display: none;">

    <!-- 引入所有模块 -->
    <script src="src/config.js"></script>
    <script src="src/ocr-services.js"></script>
    <script src="src/ui.js"></script>
    <script src="src/model-manager.js"></script>
    <script src="src/main.js"></script>
</body>
</html>
