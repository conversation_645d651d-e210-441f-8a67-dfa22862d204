* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS变量定义 - 亮色主题 */
:root {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-tertiary: #9ca3af;
    --border-primary: #e5e7eb;
    --border-secondary: #d1d5db;
    --accent-primary: #f8fafc;
    --accent-secondary: #1f2937;
    --accent-hover: #f1f5f9;
    --primary-btn-bg: #f8fafc;
    --primary-btn-text: #1f2937;
    --primary-btn-hover: #f1f5f9;
    --shadow-light: rgba(0, 0, 0, 0.05);
    --shadow-medium: rgba(0, 0, 0, 0.1);
    --shadow-heavy: rgba(0, 0, 0, 0.15);
    --success-bg: rgba(34, 197, 94, 0.1);
    --success-text: #16a34a;
    --warning-bg: rgba(251, 191, 36, 0.1);
    --warning-text: #d97706;
    --error-bg: rgba(239, 68, 68, 0.1);
    --error-text: #dc2626;
}

/* 暗色主题 */
[data-theme="dark"] {
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-tertiary: #9ca3af;
    --border-primary: #374151;
    --border-secondary: #4b5563;
    --accent-primary: #1f2937;
    --accent-secondary: #f9fafb;
    --accent-hover: #374151;
    --primary-btn-bg: #374151;
    --primary-btn-text: #f9fafb;
    --primary-btn-hover: #4b5563;
    --shadow-light: rgba(0, 0, 0, 0.2);
    --shadow-medium: rgba(0, 0, 0, 0.3);
    --shadow-heavy: rgba(0, 0, 0, 0.4);
    --success-bg: rgba(34, 197, 94, 0.2);
    --success-text: #22c55e;
    --warning-bg: rgba(251, 191, 36, 0.2);
    --warning-text: #fbbf24;
    --error-bg: rgba(239, 68, 68, 0.2);
    --error-text: #ef4444;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: var(--bg-secondary);
    color: var(--text-primary);
    overflow: hidden;
    margin: 0;
    padding: 0;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.view {
    height: 100vh;
    overflow: hidden;
}

/* 主容器布局 */
.main-container {
    display: flex;
    height: 100vh;
    background: var(--bg-secondary);
}

/* 左侧面板 */
.left-panel {
    width: 280px;
    background: var(--accent-primary);
    padding: 16px;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 20px var(--shadow-medium);
    position: relative;
    overflow: hidden;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-primary);
}

.app-title {
    color: var(--accent-secondary);
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.header-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.header-btn, .config-btn, .back-btn {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-btn:hover, .config-btn:hover, .back-btn:hover {
    background: var(--bg-secondary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--shadow-medium);
}

/* 图片预览区域 */
.image-preview-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    background: var(--bg-primary);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--border-primary);
}

.image-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.preview-img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow-light);
}

.preview-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 40px 20px;
    color: var(--text-tertiary);
}

.placeholder-icon {
    font-size: 48px;
    margin-bottom: 12px;
    opacity: 0.6;
}

.placeholder-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-secondary);
}

.placeholder-hint {
    font-size: 12px;
    opacity: 0.7;
    line-height: 1.4;
}

/* 底部控制区域 */
.bottom-controls {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 12px;
    border: 1px solid var(--border-primary);
}

.action-buttons {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.action-btn {
    flex: 1;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 12px 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    text-align: center;
    box-shadow: 0 2px 8px var(--shadow-light);
    font-size: 12px;
    font-weight: 500;
    color: var(--text-primary);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow-medium);
    background: var(--bg-tertiary);
}

.action-btn.primary {
    background: var(--primary-btn-bg);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
}

.action-btn.primary:hover {
    background: var(--primary-btn-hover);
    color: var(--primary-btn-text);
}

.action-btn .btn-icon {
    font-size: 18px;
}

.action-btn .btn-text {
    font-size: 11px;
    font-weight: 500;
}

/* 状态信息 */
.status-info {
    display: flex;
    justify-content: space-between;
    gap: 8px;
}

.status-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.status-label {
    color: var(--text-tertiary);
    font-size: 10px;
    font-weight: 500;
    margin-bottom: 2px;
}

.status-value {
    color: var(--text-secondary);
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
    background: var(--bg-tertiary);
    min-height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-secondary);
}

.status-value.ready {
    background: var(--success-bg);
    color: var(--success-text);
    border-color: var(--success-text);
}

.status-value.processing {
    background: var(--warning-bg);
    color: var(--warning-text);
    border-color: var(--warning-text);
}

.status-value.error {
    background: var(--error-bg);
    color: var(--error-text);
    border-color: var(--error-text);
}

/* 加载面板 */
.loading-panel {
    position: absolute;
    top: 50px;
    left: 16px;
    right: 16px;
    bottom: 80px;
    background: var(--accent-primary);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    text-align: center;
}

.loading-title {
    color: var(--accent-secondary);
    font-size: 14px;
    font-weight: 600;
}

.loading-desc {
    color: var(--text-tertiary);
    font-size: 12px;
}

/* 右侧面板 */
.right-panel {
    flex: 1;
    background: var(--bg-primary);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 16px;
}

.result-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-primary);
}

.result-title {
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.result-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-bottom: 12px;
}

/* 底部控制区域 */
.result-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-primary);
    gap: 12px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
    cursor: pointer;
    transition: all 0.2s ease;
}

.checkbox-label:hover {
    color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
    margin-right: 6px;
    width: auto;
    accent-color: var(--accent-primary);
}

.checkbox-text {
    font-weight: 500;
}

.control-buttons {
    display: flex;
    gap: 8px;
}

.control-btn {
    padding: 6px 12px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 4px;
}

.control-btn:hover {
    background: var(--bg-primary);
    border-color: var(--border-secondary);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px var(--shadow-light);
    color: var(--text-primary);
}

.control-btn.primary {
    background: var(--primary-btn-bg);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
}

.control-btn.primary:hover {
    background: var(--primary-btn-hover);
    color: var(--primary-btn-text);
    box-shadow: 0 2px 12px var(--shadow-medium);
}

.control-btn .btn-icon {
    font-size: 12px;
}

.control-btn .btn-text {
    font-size: 11px;
}

#re-recognize-btn {
    background: #4facfe;
    color: white;
}

#re-recognize-btn:hover {
    background: #3d8bfe;
}

.result-text {
    flex: 1;
    background: var(--bg-secondary);
    border: 2px solid var(--border-primary);
    border-radius: 12px;
    padding: 20px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
    resize: none;
    width: 100%;
    box-sizing: border-box;
    transition: all 0.3s ease;
    color: var(--text-primary);
    overflow-y: auto;
}

.result-text:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px var(--shadow-light);
    background: var(--bg-primary);
}

.result-text::placeholder {
    color: var(--text-tertiary);
    font-style: normal;
}

/* 旧的loading样式保持兼容 */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: var(--accent-secondary);
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-primary);
    border-top: 3px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 配置界面样式 */
#config-view {
    background: var(--bg-secondary);
    padding: 16px;
    overflow-y: auto;
    height: 100vh;
}

/* 自定义滚动条样式 */
#config-view::-webkit-scrollbar {
    width: 8px;
}

#config-view::-webkit-scrollbar-track {
    background: transparent;
}

#config-view::-webkit-scrollbar-thumb {
    background: var(--border-secondary);
    border-radius: 4px;
    transition: background 0.3s ease;
}

#config-view::-webkit-scrollbar-thumb:hover {
    background: var(--text-tertiary);
}

/* Firefox滚动条样式 */
#config-view {
    scrollbar-width: thin;
    scrollbar-color: var(--border-secondary) transparent;
}

.config-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: var(--bg-primary);
    border-radius: 12px;
    border: 1px solid var(--border-primary);
}

.config-title {
    color: var(--text-primary);
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

.theme-toggle {
    display: flex;
    align-items: center;
}

.theme-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-btn:hover {
    background: var(--bg-tertiary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--shadow-medium);
}

.config-content {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px var(--shadow-light);
    border: 1px solid var(--border-primary);
    max-height: calc(100vh - 120px);
    overflow-y: auto;
}

/* 配置内容滚动条样式 */
.config-content::-webkit-scrollbar {
    width: 6px;
}

.config-content::-webkit-scrollbar-track {
    background: transparent;
}

.config-content::-webkit-scrollbar-thumb {
    background: var(--border-primary);
    border-radius: 3px;
    transition: background 0.3s ease;
}

.config-content::-webkit-scrollbar-thumb:hover {
    background: var(--border-secondary);
}

.config-content {
    scrollbar-width: thin;
    scrollbar-color: var(--border-primary) transparent;
}

.config-section {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-primary);
}

.config-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.config-section h3 {
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.config-section h3::before {
    content: '⚙️';
    font-size: 14px;
}

.config-section h4 {
    color: var(--text-primary);
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 16px;
    padding: 10px 14px;
    background: var(--bg-secondary);
    border-radius: 8px;
    border-left: 3px solid var(--accent-primary);
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 13px;
}

.select-input, input[type="text"], input[type="password"], input[type="number"], textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    font-size: 13px;
    transition: all 0.3s ease;
    font-family: inherit;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.select-input:focus, input[type="text"]:focus, input[type="password"]:focus, input[type="number"]:focus, textarea:focus {
    outline: none;
    border-color: var(--border-secondary);
    box-shadow: 0 0 0 2px var(--shadow-light);
    background: var(--bg-primary);
}

/* 选择框特殊样式 */
.select-input {
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    padding-right: 32px;
}

/* 占位符样式 */
input::placeholder, textarea::placeholder {
    color: var(--text-tertiary);
    opacity: 1;
}

textarea {
    resize: vertical;
    min-height: 70px;
    line-height: 1.5;
}

/* 模型选择相关样式 */
.model-selection {
    display: flex;
    align-items: center;
}

.model-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-top: 10px;
}

.model-info h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.model-info .model-detail {
    display: flex;
    justify-content: space-between;
    margin: 5px 0;
    font-size: 13px;
}

.model-info .model-detail .label {
    font-weight: 500;
    color: #6c757d;
}

.model-info .model-detail .value {
    color: #495057;
}

.form-hint {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

.loading-models {
    color: #6c757d;
    font-style: italic;
}

.error-models {
    color: #dc3545;
    font-size: 12px;
}

/* 复选框样式 */
input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

label input[type="checkbox"] {
    margin-right: 8px;
}

/* API Key输入框和切换按钮样式 */
.input-with-toggle {
    position: relative;
    display: flex;
    align-items: center;
}

.input-with-toggle input {
    flex: 1;
    padding-right: 45px; /* 为按钮留出空间 */
}

.toggle-password {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    font-size: 16px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

.toggle-password:hover {
    background-color: var(--bg-tertiary);
}

.toggle-password:active {
    background-color: var(--bg-secondary);
}

.eye-icon {
    display: inline-block;
    transition: opacity 0.2s ease;
}

.toggle-password.hidden .eye-icon {
    opacity: 0.6;
}

.toggle-password.visible .eye-icon {
    opacity: 1;
}

/* 为不同状态提供不同的图标 */
.toggle-password.hidden .eye-icon::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background: var(--text-secondary);
    transform: rotate(45deg);
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -1px;
}

.config-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid var(--border-primary);
}

.btn {
    padding: 10px 20px;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--shadow-medium);
    background: var(--bg-tertiary);
}

.btn.primary {
    background: var(--primary-btn-bg);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
}

.btn.primary:hover {
    background: var(--primary-btn-hover);
    color: var(--primary-btn-text);
}

/* 置信度显示 */
.confidence-info {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
}

.confidence-info.high {
    background: #d4edda;
    color: #155724;
}

.confidence-info.medium {
    background: #fff3cd;
    color: #856404;
}

.confidence-info.low {
    background: #f8d7da;
    color: #721c24;
}

/* 拖拽区域样式 */
.drop-zone-content {
    text-align: center;
}

.drop-zone-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.drop-zone-text {
    font-size: 18px;
    font-weight: 500;
}

/* 进度条样式 */
.progress-container {
    width: 100%;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    text-align: center;
    font-size: 14px;
    color: #666;
}

/* 移动端布局 */
.mobile-layout .action-buttons {
    flex-direction: column;
}

.mobile-layout .config-actions {
    flex-direction: column;
}

.mobile-layout .result-actions {
    flex-direction: column;
    gap: 5px;
}

.mobile-layout .header h1 {
    font-size: 20px;
}

.mobile-layout .action-btn {
    padding: 15px;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }

    .left-panel {
        width: 100%;
        height: 200px;
        min-height: 200px;
        max-height: 200px;
        padding: 12px;
    }

    .image-preview-area {
        margin-bottom: 8px;
    }

    .placeholder-icon {
        font-size: 32px;
        margin-bottom: 8px;
    }

    .placeholder-text {
        font-size: 14px;
        margin-bottom: 4px;
    }

    .placeholder-hint {
        font-size: 11px;
    }

    .bottom-controls {
        padding: 8px;
    }

    .action-btn {
        padding: 8px 6px;
        font-size: 11px;
    }

    .action-btn .btn-icon {
        font-size: 16px;
    }

    .action-btn .btn-text {
        font-size: 10px;
    }

    .status-label {
        font-size: 9px;
    }

    .status-value {
        font-size: 10px;
        padding: 1px 4px;
    }

    .right-panel {
        padding: 12px;
        min-height: calc(100vh - 200px);
    }

    .result-controls {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .control-buttons {
        justify-content: center;
    }

    .config-content {
        padding: 20px;
        margin: 0 16px;
    }
}

@media (max-width: 480px) {
    .left-panel {
        height: 180px;
        min-height: 180px;
        max-height: 180px;
        padding: 10px;
    }

    .app-title {
        font-size: 16px;
    }

    .config-btn {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .action-buttons {
        gap: 6px;
    }

    .action-btn {
        padding: 6px 4px;
    }

    .right-panel {
        padding: 10px;
        min-height: calc(100vh - 180px);
    }

    .result-title {
        font-size: 14px;
    }

    .control-btn {
        padding: 4px 8px;
        font-size: 11px;
    }

    .checkbox-label {
        font-size: 11px;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
