<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR界面测试</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        /* 测试样式 */
        .test-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            background: white;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        .test-btn {
            margin: 5px;
            padding: 5px 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background: white;
            cursor: pointer;
        }
        .test-btn:hover {
            background: #f0f0f0;
        }
    </style>
</head>
<body>
    <div class="test-controls">
        <button class="test-btn" onclick="testImagePreview()">测试图片预览</button>
        <button class="test-btn" onclick="testResult()">测试识别结果</button>
        <button class="test-btn" onclick="testLoading()">测试加载状态</button>
        <button class="test-btn" onclick="clearAll()">清空所有</button>
    </div>

    <div id="app">
        <!-- 主界面 -->
        <div id="main-view" class="view">
            <div class="main-container">
                <!-- 左侧操作面板 -->
                <div class="left-panel">
                    <div class="panel-header">
                        <h1 class="app-title">OCR识别</h1>
                        <div class="header-controls">
                            <button id="theme-toggle-main" class="header-btn" title="切换主题">🌙</button>
                            <button id="config-btn" class="config-btn">⚙️</button>
                        </div>
                    </div>
                    
                    <!-- 图片预览区域 -->
                    <div class="image-preview-area">
                        <div id="image-preview" class="image-preview" style="display: none;">
                            <img id="preview-img" class="preview-img" alt="预览图片">
                        </div>
                        <div id="preview-placeholder" class="preview-placeholder">
                            <div class="placeholder-icon">🖼️</div>
                            <div class="placeholder-text">图片预览区域</div>
                            <div class="placeholder-hint">选择图片或截图后将在此显示</div>
                        </div>
                    </div>
                    
                    <!-- 底部控制区域 -->
                    <div class="bottom-controls">
                        <div class="action-buttons">
                            <button id="screenshot-btn" class="action-btn primary">
                                <span class="btn-icon">📷</span>
                                <span class="btn-text">截图</span>
                            </button>
                            <button id="upload-btn" class="action-btn">
                                <span class="btn-icon">📁</span>
                                <span class="btn-text">选择</span>
                            </button>
                        </div>
                        
                        <div class="status-info">
                            <div class="status-item">
                                <span class="status-label">服务:</span>
                                <span id="current-service" class="status-value">百度OCR</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">状态:</span>
                                <span id="recognition-status" class="status-value ready">就绪</span>
                            </div>
                        </div>
                    </div>
                    
                    <div id="loading" class="loading-panel" style="display: none;">
                        <div class="loading-content">
                            <div class="spinner"></div>
                            <div class="loading-text">
                                <div class="loading-title">正在识别中...</div>
                                <div class="loading-desc">请稍候，正在处理您的图片</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧结果面板 -->
                <div class="right-panel">
                    <div class="result-header">
                        <h3 class="result-title">识别结果</h3>
                    </div>
                    
                    <div class="result-content">
                        <textarea id="result-text" class="result-text" placeholder="识别结果将显示在这里，您可以直接编辑...&#10;&#10;💡 提示：&#10;• 点击左侧按钮开始识别&#10;• 支持截图和本地图片识别&#10;• 识别结果可直接编辑"></textarea>
                    </div>
                    
                    <div class="result-controls">
                        <label class="checkbox-label">
                            <input type="checkbox" id="remove-linebreaks">
                            <span class="checkbox-text">去除换行符</span>
                        </label>
                        <div class="control-buttons">
                            <button id="copy-btn" class="control-btn primary">
                                <span class="btn-icon">📋</span>
                                <span class="btn-text">复制</span>
                            </button>
                            <button id="clear-btn" class="control-btn">
                                <span class="btn-icon">🗑️</span>
                                <span class="btn-text">清空</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 测试函数
        function testImagePreview() {
            // 创建一个测试图片
            const canvas = document.createElement('canvas');
            canvas.width = 300;
            canvas.height = 200;
            const ctx = canvas.getContext('2d');
            
            // 绘制测试图片
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(0, 0, 300, 200);
            ctx.fillStyle = '#333';
            ctx.font = '20px Arial';
            ctx.fillText('测试图片', 100, 100);
            ctx.fillText('Test Image', 100, 130);
            
            const testImage = canvas.toDataURL();
            
            // 显示预览
            const imagePreview = document.getElementById('image-preview');
            const previewImg = document.getElementById('preview-img');
            const placeholder = document.getElementById('preview-placeholder');
            
            previewImg.src = testImage;
            imagePreview.style.display = 'flex';
            placeholder.style.display = 'none';
        }

        function testResult() {
            const resultText = document.getElementById('result-text');
            resultText.value = '这是一个测试识别结果\n包含多行文字\n用于测试界面显示效果\n\n测试内容：\n• 中文识别\n• 英文识别 English Text\n• 数字识别 123456\n• 符号识别 !@#$%^&*()';
        }

        function testLoading() {
            const loading = document.getElementById('loading');
            const status = document.getElementById('recognition-status');
            
            loading.style.display = 'block';
            status.textContent = '识别中';
            status.className = 'status-value processing';
            
            // 3秒后隐藏
            setTimeout(() => {
                loading.style.display = 'none';
                status.textContent = '就绪';
                status.className = 'status-value ready';
            }, 3000);
        }

        function clearAll() {
            // 清空图片预览
            const imagePreview = document.getElementById('image-preview');
            const placeholder = document.getElementById('preview-placeholder');
            imagePreview.style.display = 'none';
            placeholder.style.display = 'flex';
            
            // 清空结果
            document.getElementById('result-text').value = '';
            
            // 隐藏加载
            document.getElementById('loading').style.display = 'none';
            
            // 重置状态
            const status = document.getElementById('recognition-status');
            status.textContent = '就绪';
            status.className = 'status-value ready';
        }

        // 主题切换功能
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            // 更新主题切换按钮的图标
            const themeBtn = document.getElementById('theme-toggle-main');
            const icon = newTheme === 'dark' ? '☀️' : '🌙';
            const title = newTheme === 'dark' ? '切换到亮色主题' : '切换到暗色主题';
            themeBtn.textContent = icon;
            themeBtn.title = title;
        }

        // 加载保存的主题
        function loadTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);

            const themeBtn = document.getElementById('theme-toggle-main');
            const icon = savedTheme === 'dark' ? '☀️' : '🌙';
            const title = savedTheme === 'dark' ? '切换到亮色主题' : '切换到暗色主题';
            themeBtn.textContent = icon;
            themeBtn.title = title;
        }

        // 添加基本的事件监听
        document.getElementById('clear-btn').addEventListener('click', clearAll);
        document.getElementById('copy-btn').addEventListener('click', () => {
            const text = document.getElementById('result-text').value;
            if (text) {
                navigator.clipboard.writeText(text).then(() => {
                    alert('已复制到剪贴板');
                });
            }
        });

        document.getElementById('theme-toggle-main').addEventListener('click', toggleTheme);

        // 页面加载时初始化主题
        loadTheme();
    </script>
</body>
</html>
