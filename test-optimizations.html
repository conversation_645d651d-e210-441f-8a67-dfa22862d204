<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR插件优化测试</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        .test-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            background: var(--bg-primary);
        }
        .test-title {
            color: var(--text-primary);
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .test-item {
            margin-bottom: 15px;
            padding: 10px;
            background: var(--bg-secondary);
            border-radius: 6px;
        }
        .test-label {
            color: var(--text-secondary);
            font-size: 14px;
            margin-bottom: 5px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pass { background: #22c55e; }
        .status-fail { background: #ef4444; }
        .status-pending { background: #f59e0b; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-section">
            <h2 class="test-title">🎨 主题优化测试</h2>
            
            <div class="test-item">
                <div class="test-label">
                    <span class="status-indicator status-pass"></span>
                    亮色模式色调调整（更柔和护眼）
                </div>
                <p>亮色模式已调暗一个层级，背景色从纯白调整为更柔和的浅灰色调</p>
            </div>
            
            <div class="test-item">
                <div class="test-label">
                    <span class="status-indicator status-pass"></span>
                    主题切换按钮
                </div>
                <button id="theme-toggle-test" class="theme-btn" title="切换主题">🌓</button>
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">🔧 配置页面优化测试</h2>

            <div class="test-item">
                <div class="test-label">
                    <span class="status-indicator status-pass"></span>
                    配置界面滚动条优化（已移除自定义滚动条）
                </div>
                <p>配置页面现在使用浏览器默认滚动条，确保可访问性和流畅性</p>
            </div>

            <div class="test-item">
                <div class="test-label">
                    <span class="status-indicator status-pass"></span>
                    LLM平台选择框统一样式
                </div>
                <select id="llm-platform-test" style="width: 200px;">
                    <option value="">请选择平台</option>
                    <option value="openai">OpenAI</option>
                    <option value="anthropic">Anthropic (Claude)</option>
                    <option value="google">Google (Gemini)</option>
                    <option value="custom">自定义平台</option>
                </select>
            </div>

            <div class="test-item">
                <div class="test-label">
                    <span class="status-indicator status-pass"></span>
                    模型版本选择框样式（不会默认显示Claude）
                </div>
                <div class="model-selection">
                    <select id="llm-model-select-test" style="flex: 1;">
                        <option value="">请选择模型</option>
                        <option value="gpt-4o">GPT-4 Omni</option>
                        <option value="claude-3-5-sonnet">Claude 3.5 Sonnet</option>
                        <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                    </select>
                    <button type="button" class="btn-small" style="margin-left: 10px;">刷新</button>
                </div>
            </div>
            
            <div class="test-item">
                <div class="test-label">
                    <span class="status-indicator status-pass"></span>
                    模型信息区域主题适配
                </div>
                <div class="model-info">
                    <h5>模型信息</h5>
                    <div class="model-detail">
                        <span class="label">模型名称:</span>
                        <span class="value">GPT-4 Omni</span>
                    </div>
                    <div class="model-detail">
                        <span class="label">最大Token:</span>
                        <span class="value">4096</span>
                    </div>
                    <div class="model-detail">
                        <span class="label">支持功能:</span>
                        <span class="value">视觉识别、文本生成</span>
                    </div>
                </div>
            </div>
            
            <div class="test-item">
                <div class="test-label">
                    <span class="status-indicator status-pass"></span>
                    表单元素主题适配
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <input type="text" placeholder="文本输入框" style="width: 100%;">
                    <input type="password" placeholder="密码输入框" style="width: 100%;">
                    <input type="number" placeholder="数字输入框" style="width: 100%;">
                    <textarea placeholder="文本区域" rows="3" style="width: 100%;"></textarea>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">🖱️ 按钮配色一致性测试</h2>

            <div class="test-item">
                <div class="test-label">
                    <span class="status-indicator status-pass"></span>
                    主界面按钮配色一致性（截图vs选择）
                </div>
                <div class="action-buttons" style="width: 300px;">
                    <button class="action-btn primary">
                        <span class="btn-icon">📷</span>
                        <span class="btn-text">截图（主要）</span>
                    </button>
                    <button class="action-btn">
                        <span class="btn-icon">📁</span>
                        <span class="btn-text">选择（次要）</span>
                    </button>
                </div>
                <p style="font-size: 12px; color: var(--text-tertiary); margin-top: 8px;">
                    截图按钮为主要按钮，选择按钮为次要按钮，形成清晰的视觉层次
                </p>
            </div>

            <div class="test-item">
                <div class="test-label">
                    <span class="status-indicator status-pass"></span>
                    右侧控制按钮配色一致性（复制vs清空）
                </div>
                <div class="control-buttons">
                    <button class="control-btn primary">
                        <span class="btn-icon">📋</span>
                        <span class="btn-text">复制（主要）</span>
                    </button>
                    <button class="control-btn">
                        <span class="btn-icon">🗑️</span>
                        <span class="btn-text">清空（次要）</span>
                    </button>
                </div>
                <p style="font-size: 12px; color: var(--text-tertiary); margin-top: 8px;">
                    复制按钮为主要按钮，清空按钮为次要按钮，保持一致的层次关系
                </p>
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">⚙️ 默认服务设置测试</h2>
            
            <div class="test-item">
                <div class="test-label">
                    <span class="status-indicator status-pass"></span>
                    默认OCR服务设置为百度OCR
                </div>
                <select class="select-input" style="width: 200px;">
                    <option value="baidu" selected>百度OCR</option>
                    <option value="tencent">腾讯云OCR</option>
                    <option value="aliyun">阿里云OCR</option>
                    <option value="llm">AI大模型</option>
                </select>
            </div>
            
            <div class="test-item">
                <div class="test-label">
                    <span class="status-indicator status-pass"></span>
                    状态显示测试
                </div>
                <div class="status-info" style="width: 300px;">
                    <div class="status-item">
                        <span class="status-label">服务:</span>
                        <span class="status-value">百度OCR</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">状态:</span>
                        <span class="status-value ready">就绪</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 主题切换功能
        document.getElementById('theme-toggle-test').addEventListener('click', function() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            document.documentElement.setAttribute('data-theme', newTheme);
            
            // 更新按钮图标
            this.textContent = newTheme === 'dark' ? '☀️' : '🌙';
            this.title = newTheme === 'dark' ? '切换到亮色主题' : '切换到暗色主题';
        });

        // 加载保存的主题
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
        
        const themeBtn = document.getElementById('theme-toggle-test');
        themeBtn.textContent = savedTheme === 'dark' ? '☀️' : '🌙';
        themeBtn.title = savedTheme === 'dark' ? '切换到亮色主题' : '切换到暗色主题';
    </script>
</body>
</html>
