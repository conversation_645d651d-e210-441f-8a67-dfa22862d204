// 配置管理模块
class ConfigManager {
    constructor() {
        this.defaultConfig = {
            service: 'baidu', // 默认使用百度OCR
            baidu: {
                apiKey: '',
                secretKey: '',
                type: 'general_basic'
            },
            tencent: {
                secretId: '',
                secretKey: '',
                region: 'ap-beijing'
            },
            aliyun: {
                accessKey: '',
                accessSecret: '',
                region: 'cn-shanghai'
            },
            llm: {
                platform: 'openai',
                model: 'gpt-4-vision-preview',
                useCustomModel: false,
                customModel: '',
                // 每个平台独立的API Key存储
                apiKeys: {
                    openai: '',
                    anthropic: '',
                    google: '',
                    custom: ''
                },
                baseUrl: '',
                maxTokens: 1000,
                prompt: '请识别图片中的所有文字内容，直接输出文字，不要添加任何解释。'
            },
            ui: {
                autoHide: true,
                copyAfterOCR: false,
                showNotification: true,
                removeLinebreaks: false
            }
        };
    }

    // 获取配置
    getConfig() {
        const config = window.ocrAPI.db.get('ocr-config');
        return config ? { ...this.defaultConfig, ...config } : this.defaultConfig;
    }

    // 保存配置
    saveConfig(config) {
        const configToSave = {
            _id: 'ocr-config',
            ...config
        };

        // 如果配置已存在，需要保留_rev
        const existingConfig = window.ocrAPI.db.get('ocr-config');
        if (existingConfig && existingConfig._rev) {
            configToSave._rev = existingConfig._rev;
        }

        const result = window.ocrAPI.db.put(configToSave);
        if (result.ok) {
            configToSave._rev = result.rev;
            return { success: true, config: configToSave };
        } else {
            return { success: false, error: result.message || '保存失败' };
        }
    }

    // 验证配置
    validateConfig(config) {
        const service = config.service;
        const serviceConfig = config[service];

        if (!serviceConfig) {
            return { valid: false, error: '服务配置不存在' };
        }

        switch (service) {
            case 'baidu':
                if (!serviceConfig.apiKey || !serviceConfig.secretKey) {
                    return { valid: false, error: '百度OCR需要API Key和Secret Key' };
                }
                break;
            case 'tencent':
                if (!serviceConfig.secretId || !serviceConfig.secretKey) {
                    return { valid: false, error: '腾讯云OCR需要Secret ID和Secret Key' };
                }
                break;
            case 'aliyun':
                if (!serviceConfig.accessKey || !serviceConfig.accessSecret) {
                    return { valid: false, error: '阿里云OCR需要Access Key和Access Secret' };
                }
                break;
            case 'llm':
                const platform = serviceConfig.platform || 'openai';
                const apiKey = serviceConfig.apiKeys?.[platform] || serviceConfig.apiKey || '';

                if (!apiKey) {
                    const platformNames = {
                        openai: 'OpenAI',
                        anthropic: 'Anthropic',
                        google: 'Google',
                        custom: '自定义平台'
                    };
                    const platformName = platformNames[platform] || platform;
                    return { valid: false, error: `${platformName}平台需要API Key` };
                }

                // 验证模型配置
                if (serviceConfig.useCustomModel) {
                    if (!serviceConfig.customModel) {
                        return { valid: false, error: '使用自定义模型时需要输入模型名称' };
                    }
                } else {
                    if (!serviceConfig.model) {
                        return { valid: false, error: '请选择模型版本' };
                    }
                }
                break;
            default:
                return { valid: false, error: '不支持的服务类型' };
        }

        return { valid: true };
    }

    // 获取服务配置
    getServiceConfig(config) {
        const service = config.service;
        const serviceConfig = config[service] || {};

        switch (service) {
            case 'baidu':
                return {
                    apiKey: serviceConfig.apiKey,
                    secretKey: serviceConfig.secretKey,
                    type: serviceConfig.type || 'general_basic'
                };
            case 'tencent':
                return {
                    secretId: serviceConfig.secretId,
                    secretKey: serviceConfig.secretKey,
                    region: serviceConfig.region || 'ap-beijing'
                };
            case 'aliyun':
                return {
                    accessKey: serviceConfig.accessKey,
                    accessSecret: serviceConfig.accessSecret,
                    region: serviceConfig.region || 'cn-shanghai'
                };
            case 'llm':
                const platform = serviceConfig.platform || 'openai';
                const finalModel = serviceConfig.useCustomModel && serviceConfig.customModel
                    ? serviceConfig.customModel
                    : serviceConfig.model || 'gpt-4-vision-preview';

                // 获取当前平台的API Key
                const apiKey = serviceConfig.apiKeys?.[platform] || serviceConfig.apiKey || '';

                return {
                    platform: platform,
                    model: finalModel,
                    useCustomModel: serviceConfig.useCustomModel || false,
                    customModel: serviceConfig.customModel || '',
                    apiKey: apiKey,
                    apiKeys: serviceConfig.apiKeys || this.defaultConfig.llm.apiKeys,
                    baseUrl: serviceConfig.baseUrl,
                    maxTokens: serviceConfig.maxTokens || 1000,
                    prompt: serviceConfig.prompt || this.defaultConfig.llm.prompt
                };
            default:
                return {};
        }
    }

    // 重置配置
    resetConfig() {
        const result = window.ocrAPI.db.remove('ocr-config');
        return result.ok;
    }

    // 导出配置
    exportConfig() {
        const config = this.getConfig();
        // 移除敏感信息
        const exportConfig = JSON.parse(JSON.stringify(config));
        
        // 清空API密钥等敏感信息
        if (exportConfig.baidu) {
            exportConfig.baidu.apiKey = '';
            exportConfig.baidu.secretKey = '';
        }
        if (exportConfig.tencent) {
            exportConfig.tencent.secretId = '';
            exportConfig.tencent.secretKey = '';
        }
        if (exportConfig.aliyun) {
            exportConfig.aliyun.accessKey = '';
            exportConfig.aliyun.accessSecret = '';
        }
        if (exportConfig.llm) {
            exportConfig.llm.apiKey = '';
        }

        return JSON.stringify(exportConfig, null, 2);
    }

    // 导入配置
    importConfig(configJson) {
        try {
            const importedConfig = JSON.parse(configJson);
            const currentConfig = this.getConfig();
            
            // 合并配置，保留当前的敏感信息
            const mergedConfig = {
                ...currentConfig,
                ...importedConfig,
                baidu: {
                    ...currentConfig.baidu,
                    ...importedConfig.baidu,
                    // 如果导入的配置中没有密钥，保留当前的
                    apiKey: importedConfig.baidu?.apiKey || currentConfig.baidu?.apiKey || '',
                    secretKey: importedConfig.baidu?.secretKey || currentConfig.baidu?.secretKey || ''
                },
                tencent: {
                    ...currentConfig.tencent,
                    ...importedConfig.tencent,
                    secretId: importedConfig.tencent?.secretId || currentConfig.tencent?.secretId || '',
                    secretKey: importedConfig.tencent?.secretKey || currentConfig.tencent?.secretKey || ''
                },
                aliyun: {
                    ...currentConfig.aliyun,
                    ...importedConfig.aliyun,
                    accessKey: importedConfig.aliyun?.accessKey || currentConfig.aliyun?.accessKey || '',
                    accessSecret: importedConfig.aliyun?.accessSecret || currentConfig.aliyun?.accessSecret || ''
                },
                llm: {
                    ...currentConfig.llm,
                    ...importedConfig.llm,
                    apiKey: importedConfig.llm?.apiKey || currentConfig.llm?.apiKey || ''
                }
            };

            return this.saveConfig(mergedConfig);
        } catch (error) {
            return { success: false, error: '配置格式错误: ' + error.message };
        }
    }
}

// 导出配置管理器
window.ConfigManager = ConfigManager;
