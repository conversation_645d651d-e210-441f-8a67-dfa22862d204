// uTools插件预加载脚本

// 保存当前功能代码和payload
let currentFeatureCode = null;
let currentPayload = null;

// 监听uTools进入事件
if (typeof utools !== 'undefined') {
    // 设置插件进入监听
    try {
        // uTools的onPluginEnter事件监听
        utools.onPluginEnter(({ code, type, payload }) => {
            console.log('Plugin entered:', { code, type, payload });
            currentFeatureCode = code;
            currentPayload = payload;
            window.currentFeatureCode = code;
            window.currentPayload = payload;
            
            // 触发自定义事件通知主程序
            window.dispatchEvent(new CustomEvent('utools-plugin-enter', {
                detail: { code, type, payload }
            }));
        });
        
        // 监听主窗口推送事件（用于处理图片匹配）
        utools.onMainPush(({ code, type, payload }) => {
            console.log('Main push received:', { code, type, payload });
            currentFeatureCode = code;
            currentPayload = payload;
            window.currentFeatureCode = code;
            window.currentPayload = payload;
            
            // 触发自定义事件通知主程序
            window.dispatchEvent(new CustomEvent('utools-plugin-enter', {
                detail: { code, type, payload }
            }));
            
            // 返回true表示进入插件应用
            return true;
        });
        
        // 监听插件退出
        utools.onPluginOut(() => {
            console.log('Plugin exited');
            currentFeatureCode = null;
            currentPayload = null;
            window.currentFeatureCode = null;
            window.currentPayload = null;
        });
    } catch (error) {
        console.error('设置uTools事件监听失败:', error);
    }
} else {
    console.warn('uTools API 不可用，可能在开发环境中运行');
}

// 暴露API给渲染进程
window.ocrAPI = {
    // 屏幕截图
    screenCapture: (callback) => {
        if (typeof utools !== 'undefined' && utools.screenCapture) {
            let screenshotCompleted = false;

            // 设置超时处理，如果15秒内没有响应就认为用户取消了截图
            const timeout = setTimeout(() => {
                if (!screenshotCompleted) {
                    console.log('截图超时，可能用户取消了截图或截图失败');
                    screenshotCompleted = true;
                    if (callback) callback(null);
                }
            }, 15000); // 15秒超时，这是一个合理的时间

            try {
                utools.screenCapture((image) => {
                    if (!screenshotCompleted) {
                        screenshotCompleted = true;
                        clearTimeout(timeout);
                        console.log('截图回调执行:', image ? '成功' : '失败或取消');
                        if (callback) callback(image);
                    }
                });

                console.log('截图API调用成功，等待用户操作...');
            } catch (error) {
                if (!screenshotCompleted) {
                    screenshotCompleted = true;
                    clearTimeout(timeout);
                    console.error('截图API调用失败:', error);
                    if (callback) callback(null);
                }
            }
        } else {
            console.error('screenCapture API 不可用');
            if (callback) callback(null);
        }
    },

    // 数据库操作
    db: {
        get: (id) => utools.db.get(id),
        put: (doc) => utools.db.put(doc),
        remove: (id) => utools.db.remove(id)
    },

    // 复制到剪贴板
    copyText: (text) => {
        if (typeof utools !== 'undefined' && utools.copyText) {
            utools.copyText(text);
        } else {
            console.error('copyText API 不可用');
        }
    },

    // 隐藏窗口
    hideMainWindow: () => {
        if (typeof utools !== 'undefined' && utools.hideMainWindow) {
            utools.hideMainWindow();
        } else {
            console.error('hideMainWindow API 不可用');
        }
    },

    // 显示窗口
    showMainWindow: () => {
        if (typeof utools !== 'undefined' && utools.showMainWindow) {
            utools.showMainWindow();
        } else {
            console.error('showMainWindow API 不可用');
        }
    },

    // 获取当前功能代码
    getCurrentFeature: () => {
        // 优先从保存的功能代码获取
        if (currentFeatureCode) {
            return currentFeatureCode;
        }
        
        if (window.currentFeatureCode) {
            return window.currentFeatureCode;
        }
        
        // 尝试从uTools API获取
        try {
            // 检查是否有payload，根据payload类型推断功能
            let payload = currentPayload || window.currentPayload;
            
            // 只有在uTools API可用且getPayload方法存在时才调用
            if (!payload && typeof utools !== 'undefined' && typeof utools.getPayload === 'function') {
                payload = utools.getPayload();
            }
            
            if (payload) {
                console.log('Payload detected:', payload);
                if (payload.type === 'img') {
                    currentFeatureCode = 'ocr-image';
                    return 'ocr-image';
                } else if (payload.type === 'files') {
                    currentFeatureCode = 'ocr-image';
                    return 'ocr-image';
                }
            }
        } catch (error) {
            console.error('获取payload失败:', error);
        }
        
        // 默认返回null，让主程序处理
        return null;
    },

    // 获取payload数据
    getPayload: () => {
        try {
            let payload = currentPayload || window.currentPayload;

            // 只有在uTools API可用且getPayload方法存在时才调用
            if (!payload && typeof utools !== 'undefined' && typeof utools.getPayload === 'function') {
                payload = utools.getPayload();
            }

            return payload || null;
        } catch (error) {
            console.error('获取payload失败:', error);
            return null;
        }
    },

    // 读取文件（如果uTools支持）
    readFile: (filePath) => {
        try {
            if (typeof utools !== 'undefined' && typeof utools.readFile === 'function') {
                return utools.readFile(filePath);
            } else {
                console.log('uTools readFile API 不可用');
                return null;
            }
        } catch (error) {
            console.error('读取文件失败:', error);
            return null;
        }
    },

    // 检查文件是否存在（如果uTools支持）
    fileExists: (filePath) => {
        try {
            if (typeof utools !== 'undefined' && typeof utools.fileExists === 'function') {
                return utools.fileExists(filePath);
            } else if (typeof utools !== 'undefined' && typeof utools.readFile === 'function') {
                // 尝试读取文件来检查是否存在
                try {
                    const result = utools.readFile(filePath);
                    return result !== null && result !== undefined;
                } catch (e) {
                    return false;
                }
            } else {
                console.log('uTools文件检查API不可用');
                return false;
            }
        } catch (error) {
            console.error('检查文件存在性失败:', error);
            return false;
        }
    }
};
